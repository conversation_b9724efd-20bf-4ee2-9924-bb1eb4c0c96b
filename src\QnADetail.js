import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
  Modal,
  Dimensions,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import ApiClient from './services/ApiClient';
import FastImage from 'react-native-fast-image';
import { PinchGestureHandler, State } from 'react-native-gesture-handler';
import {
  Text,
  IconButton,
  useTheme,
  Appbar,
} from 'react-native-paper';
import { useNavigation, useRoute } from '@react-navigation/native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useUserProgress } from './store/UserProgressContext';
import { usePreferencesContext } from './store/PreferencesContext';
import { shuffleAndReorderChoices } from './utils/shuffleAndReorderChoices';
import { useExamContext } from './store/ExamContext';
import apiClient from './services/ApiClient';
import { red100 } from 'react-native-paper/lib/typescript/styles/themes/v2/colors';
import ScaledImage from './components/ScaledImage';
import { useFocusEffect } from '@react-navigation/native';
import { usePurchase } from './store/PurchaseContext';
import { useLogin } from './store/LoginContext';
import {
  GoogleSignin,
} from '@react-native-google-signin/google-signin';
import StickyBottomAdMob from './components/StickyBottomAdMob';
import { useSubscriptionRefresh } from './utils/subscriptionUtils';

const QnaDetail = () => {
  const [dimensions, setDimensions] = useState({
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', handleDimensionsChange);
    return () => subscription?.remove();
  }, []);

  const handleDimensionsChange = ({ window }) => {
    setDimensions({
      width: window.width,
      height: window.height
    });
  };

  const scrollViewRef = useRef(null);
  const { colors } = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  const { selectedExam } = useExamContext();
  const {
    initialIndex = 0,
    activeTabLabel = 'All',
    userAnswers = {},
    fromQuizResult = false
  } = route.params;

  const filteredData = route.params.filteredData || [];
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const currentItem = filteredData[currentIndex] || null;

  const [shuffledChoices, setShuffledChoices] = useState([]);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const [showAnswer, setShowAnswer] = useState(false);
  const [manualShowAnswer, setManualShowAnswer] = useState(false);
  const [selectedChoice, setSelectedChoice] = useState([]);

  const [mappedAnswer, setMappedAnswer] = useState([]);
  const [newKeyToOriginalKeyMap, setNewKeyToOriginalKeyMap] = useState({});

  const { shouldShuffleChoices } = usePreferencesContext();

  const { progress, updateProgress, toggleBookmark } = useUserProgress();
  const isBookmarked = Boolean(
    selectedExam?.id &&
    currentItem?.subject &&
    currentItem?.id &&
    progress[selectedExam.id]?.[currentItem.subject]?.bookmarked?.some(b => b.id === currentItem.id)
  );

  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const [selectedImageUri, setSelectedImageUri] = useState(null);
  const [scale, setScale] = useState(1);
  const [adError, setAdError] = useState(null);
  const [isReportModalVisible, setIsReportModalVisible] = useState(false);
  const [reportText, setReportText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleImagePress = (uri) => {
    if (!uri) {
      console.warn('Attempted to open image modal with null URI');
      return;
    }
    setSelectedImageUri(uri);
    setIsImageModalVisible(true);
    setScale(1); // Reset scale when opening
  };

  const handlePinchGesture = (event) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      setScale(event.nativeEvent.scale);
    }
  };
  const renderHtmlContent = (htmlContent, isChoice) => {
    const parts = htmlContent.split(/(<img[^>]+>)/g);

    return parts.map((part, index) => {
      if (part.startsWith('<img')) {
        const srcMatch = part.match(/src="([^"]+)"/);
        if (!srcMatch) return null;
        let src = srcMatch[1]
          .split('/')
          .map(segment => encodeURIComponent(segment))
          .join('%2F');

        src = apiClient.baseUrl + '/image/' + src;

        return (
          <TouchableOpacity
            key={`img-${index}`}
            onPress={() => handleImagePress(src)}
            style={{ flexDirection: 'row' }}
          >
            <ScaledImage
              uri={src}
              width={isChoice ? (dimensions.width - 85) : dimensions.width - 35}
            />
          </TouchableOpacity>
        );
      } else {
        const isAfterImage = index > 0 && parts[index - 1].startsWith('<img');
        const isBeforeImage = index < parts.length - 1 && parts[index + 1].startsWith('<img');
        let processedPart = part;

        if (isAfterImage) {
          processedPart = processedPart
            .replace(/^(\s*<br\s*\/?>\s*)+/gi, '')
            .replace(/^\n+/g, '');
        }

        if (isBeforeImage) {
          processedPart = processedPart
            .replace(/(\s*<br\s*\/?>\s*)+$/gi, '')
            .replace(/\n+$/g, '');
        }

        if (processedPart.trim() === '') return null;

        const textParts = processedPart.split(/<br\s*\/?>/gi);

        return textParts.map((textSegment, textIndex) => (
          <Text
            key={`text-${index}-${textIndex}`}
            style={{
              color: colors.onSurface,
              fontSize: 16,
              lineHeight: 24,
              textAlign: isChoice ? 'left' : 'justify'
            }}
          >
            {textSegment.replace(/<\/?[^>]+(>|$)/g, '')}
          </Text>
        ));
      }
    });
  };

  // Create a ref for the explanation section
  const explanationRef = useRef(null);

  // Function to scroll to explanation
  const scrollToExplanation = useCallback(() => {
    if (scrollViewRef.current && explanationRef.current && explanationRef.current.pageY) {
      // Scroll to the explanation section with a small offset to make it visible at the top
      scrollViewRef.current.scrollTo({
        y: explanationRef.current.pageY - 20, // Subtract offset to position it nicely
        animated: true
      });
    } else {
      // Fallback if measure fails
      scrollViewRef.current?.scrollTo({
        y: 300, // Approximate position
        animated: true
      });
    }
  }, []);

  const { subscriptionActive, getSubscriptionsInfo } = usePurchase();
  const { isLoggedIn } = useLogin() || {};

  useSubscriptionRefresh();

  useEffect(() => {
    if (showAnswer && currentItem?.ai_explanation) {
      // Small delay to ensure the explanation component is rendered
      setTimeout(scrollToExplanation, 150);
    }
  }, [showAnswer, currentItem?.ai_explanation, scrollToExplanation]);

  useEffect(() => {
    if (currentItem?.choices && currentItem.choices.length > 0) {
      // Remove "(correct answer)" from choices text in production only
      const cleanChoices = __DEV__
        ? currentItem.choices
        : currentItem.choices.map(choice => {
          const key = Object.keys(choice)[0];
          const value = choice[key].replace(' (correct answer)', '').trim();
          return { [key]: value };
        });

      if (fromQuizResult || !shouldShuffleChoices) {
        // Maintain original order when shuffle is disabled or in quiz review
        const map = {};
        cleanChoices.forEach(choice => {
          const key = Object.keys(choice)[0];
          map[key] = key;
        });

        // Batch all state updates
        setShuffledChoices(cleanChoices);
        setMappedAnswer(currentItem.answer);
        setNewKeyToOriginalKeyMap(map);
      } else {
        const { shuffledChoices, mappedAnswer, newKeyToOriginalKeyMap } =
          shuffleAndReorderChoices(cleanChoices, currentItem.answer);

        // Batch all state updates
        setShuffledChoices(shuffledChoices);
        setMappedAnswer(mappedAnswer);
        setNewKeyToOriginalKeyMap(newKeyToOriginalKeyMap);
      }
    }
  }, [currentItem?.id, fromQuizResult, shouldShuffleChoices]); // More specific dependencies

  const handleBookmarkToggle = () => {
    if (!selectedExam?.id || !currentItem?.subject || !currentItem?.id) return;
    toggleBookmark(selectedExam.id, currentItem.subject, currentItem.id);
  };

  useEffect(() => {
    if (selectedExam?.id && currentItem?.subject && currentItem?.id) {
      updateProgress(selectedExam.id, currentItem.subject, currentItem.id);
    }
  }, [currentItem, selectedExam, updateProgress]);

  useEffect(() => {
    if (currentItem?.choices) {
    } else {
      // If no valid item, go back to prevent blank screen
      if (filteredData.length === 0) {
        navigation.goBack();
      }
    }
  }, [currentItem, filteredData, navigation]);

  // Function to scroll to top of question content
  const scrollToQuestionTop = useCallback(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        y: 0,
        animated: false // Changed to false for instant scroll
      });
    }
  }, []);

  const safeSetCurrentIndex = useCallback((newIndex) => {
    if (newIndex >= 0 && newIndex < filteredData.length && newIndex !== currentIndex) {
      // Set transitioning state to prevent content flash
      setIsTransitioning(true);

      // Scroll immediately before any state changes
      scrollToQuestionTop();

      // Batch all state updates together
      setCurrentIndex(newIndex);
      setSelectedChoice([]);
      setShowAnswer(false);
      setManualShowAnswer(false);
      setShuffledChoices([]); // Clear choices after other states

      // Reset transitioning state after a brief moment
      setTimeout(() => setIsTransitioning(false), 50);
    }
  }, [currentIndex, filteredData.length, scrollToQuestionTop]);

  const handlePrev = useCallback(() => safeSetCurrentIndex(currentIndex - 1), [currentIndex, safeSetCurrentIndex]);
  const handleNext = useCallback(() => safeSetCurrentIndex(currentIndex + 1), [currentIndex, safeSetCurrentIndex]);

  useEffect(() => {
    if (!currentItem) return;

    let userAnswer = [];

    if (fromQuizResult) {
      userAnswer = (userAnswers[currentItem.originalIndex] || []).filter(Boolean);
    } else {
      userAnswer = Array.isArray(currentItem.userSelectedAnswer)
        ? currentItem.userSelectedAnswer
        : [];
    }

    // Only update if actually different to prevent unnecessary re-renders
    if (JSON.stringify(selectedChoice) !== JSON.stringify(userAnswer)) {
      setSelectedChoice(userAnswer);
    }
  }, [currentItem?.id, currentItem?.originalIndex, fromQuizResult]); // More specific dependencies

  useEffect(() => {
    if (!manualShowAnswer && currentItem) {
      const isMC2 = currentItem.type === 'mc2';
      const shouldShowAnswer = fromQuizResult || (selectedChoice.length > 0 && !isMC2);
      setShowAnswer(shouldShowAnswer);
    }
  }, [currentItem?.type, fromQuizResult, manualShowAnswer, selectedChoice.length]); // More specific dependencies

  const toggleChoice = (key) => {
    if (!showAnswer && !fromQuizResult) {
      const originalKey = newKeyToOriginalKeyMap[key];
      setSelectedChoice(prev =>
        prev.includes(originalKey)
          ? prev.filter(k => k !== originalKey)
          : [...prev, originalKey]
      );
    } else if (!showAnswer) {
      setSelectedChoice(prev =>
        prev.includes(key)
          ? prev.filter(k => k !== key)
          : [...prev, key]
      );
    }
  };

  const handleShowAnswerToggle = () => {
    setManualShowAnswer(true);
    setShowAnswer(prev => !prev);
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Appbar.Header elevated>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={activeTabLabel} />
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 12,
          paddingVertical: 10,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 3 }}>
            <IconButton
              icon="alert-circle-outline"
              onPress={() => setIsReportModalVisible(true)}
              iconColor={colors.primary}
            />
            <Text style={{ fontSize: 14, fontWeight: '600', color: colors.onSurface }}>
              Question {currentIndex + 1} of {filteredData.length}
            </Text>
          </View>
        </View>
      </Appbar.Header>

      <ScrollView ref={scrollViewRef} contentContainerStyle={{
        paddingVertical: 16,
        paddingHorizontal: 21,
        paddingBottom: 55 // Add extra margin equal to ad container height (50) + existing padding (16)
      }}>

        {shuffledChoices.length && !isTransitioning ? (
          <>
            <Text variant="labelSmall" style={{ color: colors.onSurfaceVariant }}>
              {currentItem?.subjectName}
            </Text>

            <View>
              {renderHtmlContent(currentItem?.question || '', false)}
            </View>

            {showAnswer && currentItem?.ai_explanation ? (
              <View
                ref={explanationRef}
                onLayout={() => {
                  // Store the y position of this view when it's rendered
                  if (explanationRef.current) {
                    explanationRef.current.measure((_, __, ___, ____, _____, pageY) => {
                      explanationRef.current.pageY = pageY;
                    });
                  }
                }}
                style={{
                  marginTop: 10,
                  padding: 16,
                  backgroundColor: colors.surface,
                  borderRadius: 8,
                  borderLeftWidth: 4,
                  borderLeftColor: colors.primary,
                  borderTopLeftRadius: 4,
                  borderBottomLeftRadius: 4,
                }}
              >
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 8,
                  marginBottom: 12
                }}>
                  <Ionicons name="information-circle" size={20} color={colors.primary} />
                  <Text variant="titleSmall" style={{
                    color: colors.primary,
                    fontWeight: '700'
                  }}>
                    Explanation
                  </Text>
                </View>
                <Text style={{
                  fontSize: 14,
                  lineHeight: 20,
                  color: colors.onSurfaceVariant,  // Changed from colors.onSurface
                }}>
                  {currentItem.ai_explanation}
                </Text>
              </View>
            ) : null}

            <Text
              style={[
                styles.questionType,
                {
                  color: colors.onSurfaceVariant,
                  paddingTop: 10,
                  paddingBottom: 5
                }
              ]}>
              {(currentItem?.answer?.length || 0) > 1
                ? "Select all that apply"
                : "Choose the correct answer"}
            </Text>

            <View style={{ gap: 10 }} key={currentIndex}>
              {shuffledChoices.map((choiceObj) => {
                const [key, value] = Object.entries(choiceObj)[0];
                const isCorrect = mappedAnswer.includes(key);
                const isSelected = selectedChoice.includes(newKeyToOriginalKeyMap[key]);
                const showIncorrect = showAnswer && isSelected && !isCorrect;
                const showCorrect = showAnswer && isCorrect;

                return (
                  <TouchableOpacity
                    key={key}
                    onPress={() => toggleChoice(key)}
                    style={{
                      borderWidth: 2,
                      borderColor: showCorrect ? '#4CAF50' :
                        showIncorrect ? '#f44336' :
                          isSelected ? colors.primary : colors.surfaceVariant,
                      borderRadius: 10,
                      padding: 14,
                      paddingRight: 20,
                      backgroundColor: isSelected ? `${colors.primary}20` : colors.surface,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                      <Text style={{
                        fontSize: 16,
                        fontWeight: '500',
                        marginRight: 8,
                        lineHeight: 24, // Match text line height
                        color: colors.onSurface
                      }}>
                        {key}.
                      </Text>

                      <View style={{ flex: 1 }}>
                        {renderHtmlContent(value, true)}
                      </View>
                    </View>
                    {showCorrect && <AntDesign style={[{ marginRight: 18 }]} name="check" size={15} color={'#4CAF50'} />}
                    {showIncorrect && <Entypo name="cross" size={18} color={'#f44336'} />}
                  </TouchableOpacity>
                );
              })}
            </View>
          </>
        ) : (
          null
        )}
      </ScrollView>
      
      <StickyBottomAdMob subscriptionActive={subscriptionActive} />

      {/* Image Modal */}
      <Modal
        visible={isImageModalVisible}
        transparent={true}
        onRequestClose={() => setIsImageModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <PinchGestureHandler onGestureEvent={handlePinchGesture}>
            <FastImage
              style={[
                styles.fullImage,
                {
                  width: dimensions.width,
                  height: dimensions.height / 2,
                  transform: [{ scale }]
                },
              ]}
              source={selectedImageUri ? { uri: selectedImageUri } : null}
              resizeMode={FastImage.resizeMode.contain}
              onError={() => {
                console.log('Error loading image in modal:', selectedImageUri);
                setIsImageModalVisible(false);
              }}
            />
          </PinchGestureHandler>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setIsImageModalVisible(false)}
          >
            <Text style={{ color: 'white', fontSize: 18 }}>Close</Text>
          </TouchableOpacity>
        </View>
      </Modal>

      {/* Report Problem Modal */}
      <Modal
        visible={isReportModalVisible}
        transparent={true}
        onRequestClose={() => setIsReportModalVisible(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: 'rgba(0,0,0,0.8)' }]}>
          <View style={{
            backgroundColor: colors.surface,
            padding: 20,
            borderRadius: 8,
            width: '80%'
          }}>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              marginBottom: 16,
              color: colors.onSurface
            }}>
              Report an issue for this Q&A and Earn AI Credits!
            </Text>

            <TextInput
              style={{
                height: 120,
                borderColor: colors.border,
                borderWidth: 1,
                borderRadius: 4,
                padding: 10,
                marginBottom: 16,
                textAlignVertical: 'top',
                color: colors.onSurface
              }}
              multiline
              placeholder="Describe the issue (e.g. out-of-syllabus question, incorrect choices, missing image). Be specific. Helpful reports earn +50 AI credits for logged in user!"
              placeholderTextColor={colors.onSurfaceVariant}
              value={reportText}
              onChangeText={setReportText}
            />

            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', gap: 10 }}>
              <TouchableOpacity
                style={{
                  padding: 10,
                  borderRadius: 4
                }}
                onPress={() => setIsReportModalVisible(false)}
              >
                <Text style={{ color: colors.primary }}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  padding: 10,
                  borderRadius: 4,
                  backgroundColor: colors.primary,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: 8,
                  opacity: isSubmitting ? 0.7 : 1
                }}
                onPress={async () => {
                  if (!reportText.trim()) {
                    Alert.alert(
                      'Error',
                      'Please describe the issue before submitting',
                      [{ text: 'OK' }]
                    );
                    return;
                  }

                  setIsSubmitting(true);
                  try {
                    // Get Google user ID if available
                    let userId = 'anonymous';
                    try {
                      const googleUser = await GoogleSignin.getCurrentUser();
                      if (googleUser && googleUser.user) {
                        userId = googleUser.user.email;
                      }
                    } catch (error) {
                      console.log('Error getting Google user:', error);
                    }

                    await ApiClient.submitQnAFeedback(
                      userId,
                      currentItem._id,
                      reportText
                    );
                    Alert.alert(
                      'Thank You',
                      'Your report has been submitted successfully. If our team validates it as helpful, you\'ll earn 50 AI credits!',
                      [{
                        text: 'OK',
                        onPress: () => {
                          setIsReportModalVisible(false);
                          setReportText('');
                        }
                      }]
                    );
                  } catch (error) {
                    console.error('Feedback submission failed:', error);
                    Alert.alert(
                      'Error',
                      'Failed to submit feedback. Please try again later.',
                      [{ text: 'OK' }]
                    );
                  } finally {
                    setIsSubmitting(false);
                  }
                }}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : null}
                <Text style={{ color: '#fff' }}>Submit</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Original Bottom Bar Styling */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderTopWidth: 1,
        borderColor: colors.surfaceVariant,
        backgroundColor: colors.background,
      }}>
        <IconButton
          icon="chevron-left"
          disabled={currentIndex === 0}
          onPress={handlePrev}
        />

        <IconButton
          icon={isBookmarked ? 'bookmark' : 'bookmark-outline'}
          onPress={handleBookmarkToggle}
        />

        <IconButton
          icon={() => (
            <Image source={require('../assets/ai.png')} style={{
              height: 30,
              width: 30,
              tintColor: colors.onSurface
            }} />
          )}
          onPress={() => navigation.navigate('ChatScreen', {
            examCode: selectedExam?.id,
            qnaId: currentItem?._id
          })}
        />

        {!fromQuizResult && (
          <IconButton
            icon={() => (
              <Image source={require('../assets/answer.png')} style={{
                height: 22,
                width: 22,
                tintColor: showAnswer ? '#4CAF50' : colors.onSurface
              }} />
            )}
            onPress={handleShowAnswerToggle}
          />
        )}

        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>
          <IconButton
            icon="chevron-right"
            disabled={currentIndex === filteredData.length - 1}
            onPress={handleNext}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 12,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 10,
    marginBottom: 20,
  },
  questionType: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 10,
    marginBottom: 5,
  },
  explanationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopWidth: 1,
  },
  choiceItem: {
    borderWidth: 2,
    borderRadius: 10,
    padding: 14,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255,255,255,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImage: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height / 2,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    padding: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 5,
  },
});

export default QnaDetail;