import { useCallback, useEffect } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { useAICredit } from '../store/AICreditContext';
import { useExamContext } from '../store/ExamContext';
import { usePurchase } from '../store/PurchaseContext';
import { useLogin } from '../store/LoginContext';
import Purchases from 'react-native-purchases';
import {
  REVENUECAT_AI_CREDIT_WEEKLY,
  REVENUECAT_AI_CREDIT_MONTHLY,
  REVENUECAT_AI_CREDIT_BIMONTHLY
} from '@env';

export const useSubscriptionRefresh = () => {
  const { addCredits } = useAICredit();
  const { selectedExam } = useExamContext();
  const { getSubscriptionsInfo } = usePurchase();
  const { isLoggedIn } = useLogin() ;

  const handleRenewal = useCallback(async (subIds) => {
    console.log('----------------------------------');
    console.log('[Subscription Renewal Detected]', subIds);
    console.log('----------------------------------');

    let totalCredits = 0;
    let planTypes = [];

    for (const subId of subIds) {
      // Extract plan type from subscription ID (e.g. "aif_c01:monthly" -> "monthly")
      const planType = subId.split(':')[1];
      let creditAmount = 0;

      switch (planType) {
        case 'weekly':
          creditAmount = REVENUECAT_AI_CREDIT_WEEKLY ? parseInt(REVENUECAT_AI_CREDIT_WEEKLY) : 50;
          break;
        case 'monthly':
          creditAmount = REVENUECAT_AI_CREDIT_MONTHLY ? parseInt(REVENUECAT_AI_CREDIT_MONTHLY) : 300;
          break;
        case 'bimonthly':
          creditAmount = REVENUECAT_AI_CREDIT_BIMONTHLY ? parseInt(REVENUECAT_AI_CREDIT_BIMONTHLY) : 1000;
          break;
        default:
          console.warn(`[Subscription Renewal] Unknown plan type: ${planType}`);
          continue;
      }

      totalCredits += creditAmount;
      planTypes.push(planType);
    }

    try {
      await addCredits(totalCredits);
      console.log(`[Subscription Renewal] Added ${totalCredits} AI credits in total for plans: ${planTypes.join(', ')}`);

      // Create detailed message for each subscription
      const renewalDetails = subIds.map(subId => {
        const [examPart, planType] = subId.split(':');
        const examCode = examPart.split('_')[0].replace(/_/g, '-').toUpperCase();
        const examNumber = examPart.split('_')[1].toUpperCase();
        let creditAmount;
        switch(planType) {
          case 'weekly':
            creditAmount = REVENUECAT_AI_CREDIT_WEEKLY ? parseInt(REVENUECAT_AI_CREDIT_WEEKLY) : 50;
            break;
          case 'monthly':
            creditAmount = REVENUECAT_AI_CREDIT_MONTHLY ? parseInt(REVENUECAT_AI_CREDIT_MONTHLY) : 300;
            break;
          case 'bimonthly':
            creditAmount = REVENUECAT_AI_CREDIT_BIMONTHLY ? parseInt(REVENUECAT_AI_CREDIT_BIMONTHLY) : 1000;
            break;
          default:
            creditAmount = 0;
        }
        
        return `${creditAmount} credits for ${examCode}-${examNumber} ${planType} plan`;
      });

      Alert.alert(
        'AskAI Credits Added',
        `You've received:\n${renewalDetails.join('\n')}\n\nTotal: ${totalCredits} additional AskAI credits`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('[Subscription Renewal] Failed to add credits:', error);
      Alert.alert(
        'Error Adding Credits',
        'Failed to add credits for your subscription renewals. Please contact support if this persists.',
        [{ text: 'OK' }]
      );
    }
  }, [addCredits]);


  const refreshSubscription = useCallback(async () => {
    if (selectedExam?.exam_code && isLoggedIn) {
      console.log('[useSubscriptionRefresh] Refreshing subscription status');
      try {
        // Get current customer info from AsyncStorage
        const customerInfoString = await AsyncStorage.getItem('@RevenueCat:customerInfo');
        const customerInfo = customerInfoString ? JSON.parse(customerInfoString) : null;

        // Get previous customer info for comparison
        const prevCustomerInfoString = await AsyncStorage.getItem('@RevenueCat:prevCustomerInfo');
        const prevCustomerInfo = prevCustomerInfoString ? JSON.parse(prevCustomerInfoString) : null;

        if (customerInfo?.data) {
          // Store current as previous for next comparison
          await AsyncStorage.setItem('@RevenueCat:prevCustomerInfo', customerInfoString);

          // Check for subscription renewals if we have previous data
          if (prevCustomerInfo?.data && customerInfo.data.activeSubscriptions) {
            const renewedSubIds = [];

            for (const subId of customerInfo.data.activeSubscriptions) {
              const newExpDate = customerInfo.data.allExpirationDates[subId];
              const oldExpDate = prevCustomerInfo.data.allExpirationDates[subId];

              if (newExpDate && oldExpDate) {
                if (oldExpDate === newExpDate) {
                  console.log(`[Subscription Renewal] Checking renewal for ${subId} - Expiry: ${oldExpDate}`);
                } else {
                  console.log(`[Subscription Renewal] Checking renewal for ${subId} - Old: ${oldExpDate} | New: ${newExpDate}`);
                }
                
                // Only consider it renewal if the subscription was active in previous info
                const wasPreviouslyActive = prevCustomerInfo.data.activeSubscriptions.includes(subId);
                const isRenewed = wasPreviouslyActive && new Date(newExpDate) > new Date(oldExpDate);
                
                if (isRenewed) {
                  console.log(`[Subscription Renewal] Detected renewal for subscription ${subId}`);
                  renewedSubIds.push(subId);
                } else if (!wasPreviouslyActive) {
                  console.log(`[Subscription Renewal] ${subId} is a new purchase, not a renewal`);
                }
              }
            }

            if (renewedSubIds.length > 0) {
              await handleRenewal(renewedSubIds);
            }
          }
        }

        // Refresh subscription info from API
        console.log('[useSubscriptionRefresh] Subscription refreshed:',
          await getSubscriptionsInfo(selectedExam.exam_code, true));

      } catch (error) {
        console.error('Error checking subscription:', error);
      }
    }
  }, [selectedExam, isLoggedIn, getSubscriptionsInfo, handleRenewal]);

  // Auto-refresh when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refreshSubscription();
    }, [])
  );

  return refreshSubscription;
};

/**
 * Hook that provides subscription refresh functionality without useFocusEffect
 * This is used by the RevenueCat listener to avoid navigation context dependency
 */
export const useSubscriptionRefreshCore = () => {
  const { addCredits } = useAICredit();
  const { selectedExam } = useExamContext();
  const { getSubscriptionsInfo } = usePurchase();
  const { isLoggedIn } = useLogin();

  const handleRenewal = useCallback(async (subIds) => {
    console.log('----------------------------------');
    console.log('[Subscription Renewal Detected]', subIds);
    console.log('----------------------------------');

    let totalCredits = 0;
    let planTypes = [];

    for (const subId of subIds) {
      // Extract plan type from subscription ID (e.g. "aif_c01:monthly" -> "monthly")
      const planType = subId.split(':')[1];
      let creditAmount = 0;

      switch (planType) {
        case 'weekly':
          creditAmount = REVENUECAT_AI_CREDIT_WEEKLY ? parseInt(REVENUECAT_AI_CREDIT_WEEKLY) : 50;
          break;
        case 'monthly':
          creditAmount = REVENUECAT_AI_CREDIT_MONTHLY ? parseInt(REVENUECAT_AI_CREDIT_MONTHLY) : 300;
          break;
        case 'bimonthly':
          creditAmount = REVENUECAT_AI_CREDIT_BIMONTHLY ? parseInt(REVENUECAT_AI_CREDIT_BIMONTHLY) : 1000;
          break;
        default:
          console.warn(`[Subscription Renewal] Unknown plan type: ${planType}`);
          continue;
      }

      totalCredits += creditAmount;
      planTypes.push(planType);
    }

    try {
      await addCredits(totalCredits);
      console.log(`[Subscription Renewal] Added ${totalCredits} AI credits in total for plans: ${planTypes.join(', ')}`);

      // Create detailed message for each subscription
      const renewalDetails = subIds.map(subId => {
        const [examPart, planType] = subId.split(':');
        const examCode = examPart.split('_')[0].replace(/_/g, '-').toUpperCase();
        const examNumber = examPart.split('_')[1].toUpperCase();
        let creditAmount;
        switch(planType) {
          case 'weekly':
            creditAmount = REVENUECAT_AI_CREDIT_WEEKLY ? parseInt(REVENUECAT_AI_CREDIT_WEEKLY) : 50;
            break;
          case 'monthly':
            creditAmount = REVENUECAT_AI_CREDIT_MONTHLY ? parseInt(REVENUECAT_AI_CREDIT_MONTHLY) : 300;
            break;
          case 'bimonthly':
            creditAmount = REVENUECAT_AI_CREDIT_BIMONTHLY ? parseInt(REVENUECAT_AI_CREDIT_BIMONTHLY) : 1000;
            break;
          default:
            creditAmount = 0;
        }

        return `${creditAmount} credits for ${examCode}-${examNumber} ${planType} plan`;
      });

      Alert.alert(
        'AskAI Credits Added',
        `You've received:\n${renewalDetails.join('\n')}\n\nTotal: ${totalCredits} additional AskAI credits`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('[Subscription Renewal] Failed to add credits:', error);
      Alert.alert(
        'Error Adding Credits',
        'Failed to add credits for your subscription renewals. Please contact support if this persists.',
        [{ text: 'OK' }]
      );
    }
  }, [addCredits]);

  const refreshSubscription = useCallback(async () => {
    if (selectedExam?.exam_code && isLoggedIn) {
      console.log('[useSubscriptionRefreshCore] Refreshing subscription status');
      try {
        // Get current customer info from AsyncStorage
        const customerInfoString = await AsyncStorage.getItem('@RevenueCat:customerInfo');
        const customerInfo = customerInfoString ? JSON.parse(customerInfoString) : null;

        // Get previous customer info for comparison
        const prevCustomerInfoString = await AsyncStorage.getItem('@RevenueCat:prevCustomerInfo');
        const prevCustomerInfo = prevCustomerInfoString ? JSON.parse(prevCustomerInfoString) : null;

        if (customerInfo?.data) {
          // Store current as previous for next comparison
          await AsyncStorage.setItem('@RevenueCat:prevCustomerInfo', customerInfoString);

          // Check for subscription renewals if we have previous data
          if (prevCustomerInfo?.data && customerInfo.data.activeSubscriptions) {
            const renewedSubIds = [];

            for (const subId of customerInfo.data.activeSubscriptions) {
              const newExpDate = customerInfo.data.allExpirationDates[subId];
              const oldExpDate = prevCustomerInfo.data.allExpirationDates[subId];

              if (newExpDate && oldExpDate) {
                if (oldExpDate === newExpDate) {
                  console.log(`[Subscription Renewal] Checking renewal for ${subId} - Expiry: ${oldExpDate}`);
                } else {
                  console.log(`[Subscription Renewal] Checking renewal for ${subId} - Old: ${oldExpDate} | New: ${newExpDate}`);
                }

                // Only consider it renewal if the subscription was active in previous info
                const wasPreviouslyActive = prevCustomerInfo.data.activeSubscriptions.includes(subId);
                const isRenewed = wasPreviouslyActive && new Date(newExpDate) > new Date(oldExpDate);

                if (isRenewed) {
                  console.log(`[Subscription Renewal] Detected renewal for subscription ${subId}`);
                  renewedSubIds.push(subId);
                } else if (!wasPreviouslyActive) {
                  console.log(`[Subscription Renewal] ${subId} is a new purchase, not a renewal`);
                }
              }
            }

            if (renewedSubIds.length > 0) {
              await handleRenewal(renewedSubIds);
            }
          }
        }

        // Refresh subscription info from API
        console.log('[useSubscriptionRefreshCore] Subscription refreshed:',
          await getSubscriptionsInfo(selectedExam.exam_code, true));

      } catch (error) {
        console.error('Error checking subscription:', error);
      }
    }
  }, [selectedExam, isLoggedIn, getSubscriptionsInfo, handleRenewal]);

  return refreshSubscription;
};

/**
 * Safe version of subscription refresh that handles context availability
 */
export const useSafeSubscriptionRefreshCore = () => {
  // Access contexts normally - they should be available when this hook is called
  const { addCredits } = useAICredit();
  const { selectedExam } = useExamContext();
  const { getSubscriptionsInfo } = usePurchase();
  const { isLoggedIn } = useLogin();

  const handleRenewal = useCallback(async (subIds) => {
    if (!addCredits) {
      console.log('[useSafeSubscriptionRefreshCore] addCredits not available, skipping renewal');
      return;
    }

    console.log('----------------------------------');
    console.log('[Subscription Renewal Detected]', subIds);
    console.log('----------------------------------');

    let totalCredits = 0;
    let planTypes = [];

    for (const subId of subIds) {
      // Extract plan type from subscription ID (e.g. "aif_c01:monthly" -> "monthly")
      const planType = subId.split(':')[1];
      let creditAmount = 0;

      switch (planType) {
        case 'weekly':
          creditAmount = REVENUECAT_AI_CREDIT_WEEKLY ? parseInt(REVENUECAT_AI_CREDIT_WEEKLY) : 50;
          break;
        case 'monthly':
          creditAmount = REVENUECAT_AI_CREDIT_MONTHLY ? parseInt(REVENUECAT_AI_CREDIT_MONTHLY) : 300;
          break;
        case 'bimonthly':
          creditAmount = REVENUECAT_AI_CREDIT_BIMONTHLY ? parseInt(REVENUECAT_AI_CREDIT_BIMONTHLY) : 1000;
          break;
        default:
          console.warn(`[Subscription Renewal] Unknown plan type: ${planType}`);
          continue;
      }

      totalCredits += creditAmount;
      planTypes.push(planType);
    }

    try {
      await addCredits(totalCredits);
      console.log(`[Subscription Renewal] Added ${totalCredits} AI credits in total for plans: ${planTypes.join(', ')}`);

      // Create detailed message for each subscription
      const renewalDetails = subIds.map(subId => {
        const [examPart, planType] = subId.split(':');
        const examCode = examPart.split('_')[0].replace(/_/g, '-').toUpperCase();
        const examNumber = examPart.split('_')[1].toUpperCase();
        let creditAmount;
        switch(planType) {
          case 'weekly':
            creditAmount = REVENUECAT_AI_CREDIT_WEEKLY ? parseInt(REVENUECAT_AI_CREDIT_WEEKLY) : 50;
            break;
          case 'monthly':
            creditAmount = REVENUECAT_AI_CREDIT_MONTHLY ? parseInt(REVENUECAT_AI_CREDIT_MONTHLY) : 300;
            break;
          case 'bimonthly':
            creditAmount = REVENUECAT_AI_CREDIT_BIMONTHLY ? parseInt(REVENUECAT_AI_CREDIT_BIMONTHLY) : 1000;
            break;
          default:
            creditAmount = 0;
        }

        return `${creditAmount} credits for ${examCode}-${examNumber} ${planType} plan`;
      });

      Alert.alert(
        'AskAI Credits Added',
        `You've received:\n${renewalDetails.join('\n')}\n\nTotal: ${totalCredits} additional AskAI credits`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('[Subscription Renewal] Failed to add credits:', error);
      Alert.alert(
        'Error Adding Credits',
        'Failed to add credits for your subscription renewals. Please contact support if this persists.',
        [{ text: 'OK' }]
      );
    }
  }, [addCredits]);

  const refreshSubscription = useCallback(async () => {
    if (!selectedExam?.exam_code || !isLoggedIn || !getSubscriptionsInfo) {
      console.log('[useSafeSubscriptionRefreshCore] Required contexts not available, skipping refresh');
      return;
    }

    console.log('[useSafeSubscriptionRefreshCore] Refreshing subscription status');
    try {
      // Get current customer info from AsyncStorage
      const customerInfoString = await AsyncStorage.getItem('@RevenueCat:customerInfo');
      const customerInfo = customerInfoString ? JSON.parse(customerInfoString) : null;

      // Get previous customer info for comparison
      const prevCustomerInfoString = await AsyncStorage.getItem('@RevenueCat:prevCustomerInfo');
      const prevCustomerInfo = prevCustomerInfoString ? JSON.parse(prevCustomerInfoString) : null;

      if (customerInfo?.data) {
        // Store current as previous for next comparison
        await AsyncStorage.setItem('@RevenueCat:prevCustomerInfo', customerInfoString);

        // Check for subscription renewals if we have previous data
        if (prevCustomerInfo?.data && customerInfo.data.activeSubscriptions) {
          const renewedSubIds = [];

          for (const subId of customerInfo.data.activeSubscriptions) {
            const newExpDate = customerInfo.data.allExpirationDates[subId];
            const oldExpDate = prevCustomerInfo.data.allExpirationDates[subId];

            if (newExpDate && oldExpDate) {
              if (oldExpDate === newExpDate) {
                console.log(`[Subscription Renewal] Checking renewal for ${subId} - Expiry: ${oldExpDate}`);
              } else {
                console.log(`[Subscription Renewal] Checking renewal for ${subId} - Old: ${oldExpDate} | New: ${newExpDate}`);
              }

              // Only consider it renewal if the subscription was active in previous info
              const wasPreviouslyActive = prevCustomerInfo.data.activeSubscriptions.includes(subId);
              const isRenewed = wasPreviouslyActive && new Date(newExpDate) > new Date(oldExpDate);

              if (isRenewed) {
                console.log(`[Subscription Renewal] Detected renewal for subscription ${subId}`);
                renewedSubIds.push(subId);
              } else if (!wasPreviouslyActive) {
                console.log(`[Subscription Renewal] ${subId} is a new purchase, not a renewal`);
              }
            }
          }

          if (renewedSubIds.length > 0) {
            await handleRenewal(renewedSubIds);
          }
        }
      }

      // Refresh subscription info from API
      console.log('[useSafeSubscriptionRefreshCore] Subscription refreshed:',
        await getSubscriptionsInfo(selectedExam.exam_code, true));

    } catch (error) {
      console.error('Error checking subscription:', error);
    }
  }, [selectedExam, isLoggedIn, getSubscriptionsInfo, handleRenewal]);

  return refreshSubscription;
};

/**
 * Custom hook to set up RevenueCat customer info update listener
 * This hook should be used in the root component to listen for subscription changes
 */
export const useRevenueCatListener = () => {
  const refreshSubscription = useSafeSubscriptionRefreshCore();

  useEffect(() => {
    console.log('[useRevenueCatListener] Setting up RevenueCat customer info listener');

    const listener = Purchases.addCustomerInfoUpdateListener(customerInfo => {
      console.log('RevenueCat subscription updated:', JSON.stringify(customerInfo, null, 2));

      // Call the subscription refresh hook
      refreshSubscription();
    });

    // Cleanup function to remove the listener
    return () => {
      console.log('[useRevenueCatListener] Removing RevenueCat customer info listener');
      if (listener && typeof listener.remove === 'function') {
        listener.remove();
      }
    };
  }, [refreshSubscription]);
};